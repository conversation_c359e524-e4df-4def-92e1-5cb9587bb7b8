"use client";

import { useState, useCallback, useRef } from "react";
import {
  AgentSummary,
  AgentBuilderResponse,
  AgentUpdateResponse,
  defaultAgentSummary,
} from "@/types/agent-builder-response";
import { ChatMessage } from "@/types/agent-builder";
import { trpc } from "@/utils/trpc/client";

export interface UseAgentBuilderReturn {
  // Agent state
  currentAgentSummary: AgentSummary;
  agentHistory: AgentSummary[];

  // Chat state
  messages: ChatMessage[];
  isLoading: boolean;

  // Actions
  sendMessage: (content: string) => Promise<void>;
  updateAgentSummary: (summary: AgentSummary) => void;
  resetAgent: () => void;
  undoLastChange: () => void;

  // Utilities
  canUndo: boolean;
}

export function useAgentBuilder(): UseAgentBuilderReturn {
  // Agent state
  const [currentAgentSummary, setCurrentAgentSummary] =
    useState<AgentSummary>(defaultAgentSummary);
  const [agentHistory, setAgentHistory] = useState<AgentSummary[]>([
    defaultAgentSummary,
  ]);

  // Chat state
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Ref to track message IDs
  const messageIdCounter = useRef(0);

  // tRPC mutation for agent generation
  const generateAgentMutation = trpc.agentBuilder.generateAgent.useMutation();

  const generateMessageId = useCallback(() => {
    messageIdCounter.current += 1;
    return `msg-${messageIdCounter.current}-${Date.now()}`;
  }, []);

  const addMessage = useCallback(
    (message: Omit<ChatMessage, "id" | "timestamp">) => {
      const newMessage: ChatMessage = {
        ...message,
        id: generateMessageId(),
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, newMessage]);
      return newMessage;
    },
    [generateMessageId]
  );

  const updateAgentSummary = useCallback((summary: AgentSummary) => {
    setCurrentAgentSummary(summary);
    setAgentHistory((prev) => [...prev, summary]);
  }, []);

  const sendMessage = useCallback(
    async (content: string) => {
      if (isLoading || !content.trim()) return;

      setIsLoading(true);

      // Add user message
      const userMessage = addMessage({
        type: "user",
        content: content.trim(),
      });

      try {
        // Prepare messages for API
        const apiMessages = [...messages, userMessage].map((msg) => ({
          id: msg.id,
          role:
            msg.type === "user" ? ("user" as const) : ("assistant" as const),
          content: msg.content,
          timestamp: msg.timestamp,
        }));

        // Call the tRPC mutation
        const result = await generateAgentMutation.mutateAsync({
          messages: apiMessages,
          currentAgentSummary: currentAgentSummary,
        });

        if (!result.success) {
          throw new Error("API returned unsuccessful response");
        }

        const structuredResponse: AgentBuilderResponse | AgentUpdateResponse =
          result.data;

        // Add assistant message with structured response
        addMessage({
          type: "assistant",
          content: structuredResponse.message,
          structuredResponse,
        });

        // Update agent summary
        updateAgentSummary(structuredResponse.agentSummary);
      } catch (error) {
        console.error("Error sending message:", error);

        // Add error message
        addMessage({
          type: "assistant",
          content:
            "I apologize, but I encountered an error while processing your request. Please try again.",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [
      isLoading,
      messages,
      currentAgentSummary,
      addMessage,
      updateAgentSummary,
      generateAgentMutation,
    ]
  );

  const resetAgent = useCallback(() => {
    setCurrentAgentSummary(defaultAgentSummary);
    setAgentHistory([defaultAgentSummary]);
    setMessages([]);
  }, []);

  const undoLastChange = useCallback(() => {
    if (agentHistory.length > 1) {
      const newHistory = agentHistory.slice(0, -1);
      setAgentHistory(newHistory);
      const lastSummary = newHistory[newHistory.length - 1];
      if (lastSummary) {
        setCurrentAgentSummary(lastSummary);
      }
    }
  }, [agentHistory]);

  const canUndo = agentHistory.length > 1;

  return {
    // Agent state
    currentAgentSummary,
    agentHistory,

    // Chat state
    messages,
    isLoading,

    // Actions
    sendMessage,
    updateAgentSummary,
    resetAgent,
    undoLastChange,

    // Utilities
    canUndo,
  };
}
