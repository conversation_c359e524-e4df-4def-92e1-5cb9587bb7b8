import { openai } from "@ai-sdk/openai";
import { generateObject } from "ai";
import { NextRequest, NextResponse } from "next/server";
import {
  agentBuilderResponseSchema,
  agentUpdateResponseSchema,
  validateAgentBuilderResponse,
  validateAgentUpdateResponse,
  defaultAgentSummary,
} from "@/types/agent-builder-response";

// System prompt for agent creation
const AGENT_CREATION_SYSTEM_PROMPT = `You are an AI agent builder assistant. Your role is to help users create and iterate on AI agent configurations.

When a user describes what they want their agent to do, you should:
1. Analyze their requirements
2. Create a structured agent configuration with appropriate inputs, outputs, and process
3. Provide a helpful message explaining what you've created
4. Suggest improvements or ask clarifying questions if needed

For inputs and outputs, use appropriate types like:
- text, file, image, audio, video for content types
- database_schema, api_endpoint, json_data for data types
- python_script, javascript_code, sql_query for code types
- chart, report, analysis for output types

For icons, use lucide-react icon names like:
- message-square, file-text, image, mic, video for content
- database, globe, code, bar-chart for technical items

Always respond with valid JSON matching the required schema. Be creative but practical in your suggestions.`;

// System prompt for agent iteration
const AGENT_ITERATION_SYSTEM_PROMPT = `You are an AI agent builder assistant helping users iterate on existing agent configurations.

The user has an existing agent configuration and wants to modify it. You should:
1. Understand what changes they want to make
2. Update the agent configuration appropriately
3. Explain what changes you made and why
4. Provide suggestions for further improvements

When making changes, be thoughtful about:
- Maintaining consistency with the existing agent's purpose
- Adding value through the modifications
- Ensuring the inputs and outputs still make sense together
- Keeping the process description clear and actionable

Always respond with valid JSON matching the required schema and include details about what changed.`;

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { messages, currentAgentSummary } = body;

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: "Messages array is required" },
        { status: 400 }
      );
    }

    // Determine if this is a new agent creation or iteration
    const isIteration =
      currentAgentSummary && Object.keys(currentAgentSummary).length > 0;

    // Prepare the system prompt
    const systemPrompt = isIteration
      ? AGENT_ITERATION_SYSTEM_PROMPT
      : AGENT_CREATION_SYSTEM_PROMPT;

    // Add context about current agent if iterating
    const contextualSystemPrompt = isIteration
      ? `${systemPrompt}\n\nCurrent agent configuration:\n${JSON.stringify(currentAgentSummary, null, 2)}`
      : systemPrompt;

    // Use the appropriate schema based on whether this is iteration or creation
    const schema = isIteration
      ? agentUpdateResponseSchema
      : agentBuilderResponseSchema;

    // Generate structured response using AI SDK
    const result = await generateObject({
      model: openai("gpt-4o-mini"), // Using GPT-4 for better structured output
      system: contextualSystemPrompt,
      messages: messages.map((msg: any) => ({
        role: msg.role || msg.type,
        content: msg.content,
      })),
      schema,
      temperature: 0.7,
    });

    // Validate the response
    const validatedResponse = isIteration
      ? validateAgentUpdateResponse(result.object)
      : validateAgentBuilderResponse(result.object);

    return NextResponse.json({
      success: true,
      data: validatedResponse,
      isIteration,
    });
  } catch (error) {
    console.error("Agent builder API error:", error);

    // Return a fallback response if AI generation fails
    const fallbackResponse = {
      message:
        "I apologize, but I encountered an error while processing your request. Let me create a basic agent configuration for you.",
      agentSummary: defaultAgentSummary,
      reasoning: "Fallback response due to processing error",
    };

    return NextResponse.json({
      success: true,
      data: fallbackResponse,
      isIteration: false,
      fallback: true,
    });
  }
}

// GET endpoint for health check
export async function GET() {
  return NextResponse.json({
    status: "healthy",
    endpoint: "agent-builder",
    timestamp: new Date().toISOString(),
  });
}
