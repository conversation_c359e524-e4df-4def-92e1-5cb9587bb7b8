import { describe, it, expect } from '@jest/globals';
import { 
  validateAgentBuilderResponse, 
  validateAgentUpdateResponse,
  agentBuilderResponseSchema,
  agentUpdateResponseSchema,
  defaultAgentSummary
} from '../types/agent-builder-response';

describe('Agent Builder Response Validation', () => {
  describe('Agent Creation Response', () => {
    it('should validate a valid agent builder response', () => {
      const validResponse = {
        message: "I've created an AI agent for data analysis.",
        agentSummary: {
          name: "Data Analyzer",
          description: "An AI agent that analyzes datasets and generates insights",
          inputs: [
            {
              id: "1",
              type: "csv_file",
              label: "dataset",
              icon: "file-text",
              description: "CSV file containing the data to analyze"
            }
          ],
          outputs: [
            {
              id: "1", 
              type: "analysis_report",
              label: "analysis report",
              icon: "bar-chart",
              description: "Comprehensive analysis report with insights"
            }
          ],
          process: "Load the CSV data, perform statistical analysis, identify patterns and trends, and generate a comprehensive report with visualizations.",
          version: "v1"
        },
        reasoning: "Created a focused data analysis agent based on your requirements."
      };

      expect(() => validateAgentBuilderResponse(validResponse)).not.toThrow();
      
      const result = validateAgentBuilderResponse(validResponse);
      expect(result.message).toBe(validResponse.message);
      expect(result.agentSummary.name).toBe("Data Analyzer");
      expect(result.agentSummary.inputs).toHaveLength(1);
      expect(result.agentSummary.outputs).toHaveLength(1);
    });

    it('should reject invalid agent builder response', () => {
      const invalidResponse = {
        message: "", // Empty message should fail
        agentSummary: {
          name: "",
          description: "",
          inputs: [], // Empty inputs should fail
          outputs: [],
          process: ""
        }
      };

      expect(() => validateAgentBuilderResponse(invalidResponse)).toThrow();
    });
  });

  describe('Agent Update Response', () => {
    it('should validate a valid agent update response', () => {
      const validUpdateResponse = {
        message: "I've updated your agent to include image processing capabilities.",
        agentSummary: {
          name: "Enhanced Data Analyzer",
          description: "An AI agent that analyzes datasets and images",
          inputs: [
            {
              id: "1",
              type: "csv_file", 
              label: "dataset",
              icon: "file-text"
            },
            {
              id: "2",
              type: "image",
              label: "image data",
              icon: "image"
            }
          ],
          outputs: [
            {
              id: "1",
              type: "analysis_report",
              label: "comprehensive report",
              icon: "bar-chart"
            }
          ],
          process: "Analyze both CSV data and images to provide comprehensive insights.",
          version: "v2"
        },
        changes: [
          {
            field: "inputs",
            newValue: "Added image input capability",
            reason: "User requested image analysis functionality"
          },
          {
            field: "name", 
            oldValue: "Data Analyzer",
            newValue: "Enhanced Data Analyzer",
            reason: "Updated name to reflect new capabilities"
          }
        ],
        reasoning: "Enhanced the agent with image processing to meet your expanded requirements."
      };

      expect(() => validateAgentUpdateResponse(validUpdateResponse)).not.toThrow();
      
      const result = validateAgentUpdateResponse(validUpdateResponse);
      expect(result.agentSummary.inputs).toHaveLength(2);
      expect(result.changes).toHaveLength(2);
    });
  });

  describe('Default Agent Summary', () => {
    it('should have valid default agent summary', () => {
      expect(() => agentBuilderResponseSchema.parse({
        message: "Default agent created",
        agentSummary: defaultAgentSummary
      })).not.toThrow();
      
      expect(defaultAgentSummary.name).toBe("New AI Agent");
      expect(defaultAgentSummary.inputs).toHaveLength(1);
      expect(defaultAgentSummary.outputs).toHaveLength(1);
      expect(defaultAgentSummary.version).toBe("v1");
    });
  });

  describe('Schema Edge Cases', () => {
    it('should require at least one input and output', () => {
      const invalidSummary = {
        name: "Test Agent",
        description: "Test description", 
        inputs: [], // Should fail
        outputs: [
          {
            id: "1",
            type: "text",
            label: "output",
            icon: "message-square"
          }
        ],
        process: "Test process",
        version: "v1"
      };

      expect(() => agentBuilderResponseSchema.parse({
        message: "Test",
        agentSummary: invalidSummary
      })).toThrow();
    });

    it('should handle optional fields correctly', () => {
      const minimalValidResponse = {
        message: "Minimal agent created",
        agentSummary: {
          name: "Minimal Agent",
          description: "A minimal agent",
          inputs: [
            {
              id: "1",
              type: "text",
              label: "input",
              icon: "message-square"
            }
          ],
          outputs: [
            {
              id: "1", 
              type: "text",
              label: "output",
              icon: "message-square"
            }
          ],
          process: "Simple process"
          // version is optional and should default to "v1"
        }
        // reasoning and suggestions are optional
      };

      expect(() => validateAgentBuilderResponse(minimalValidResponse)).not.toThrow();
      
      const result = validateAgentBuilderResponse(minimalValidResponse);
      expect(result.agentSummary.version).toBe("v1"); // Should default
    });
  });
});
