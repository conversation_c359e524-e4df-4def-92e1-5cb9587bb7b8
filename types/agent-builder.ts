export type ChatMessage = {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: string;
  version?: string;
};

export type AgentVersion = {
  id: string;
  version: string;
  createdAt: string;
};

export type AgentConfiguration = {
  id: string;
  name: string;
  description: string;
  currentVersion: string;
  versions: AgentVersion[];
  inputs: AgentInput[];
  outputs: AgentOutput[];
  process: string;
};

export type AgentInput = {
  id: string;
  type: string;
  label: string;
  icon: string;
};

export type AgentOutput = {
  id: string;
  type: string;
  label: string;
  icon: string;
};
