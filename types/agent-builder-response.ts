import { z } from "zod";

// Base schemas for agent components
export const agentInputSchema = z.object({
  id: z.string(),
  type: z.string(),
  label: z.string(),
  icon: z.string(),
  description: z.string().optional(),
});

export const agentOutputSchema = z.object({
  id: z.string(),
  type: z.string(),
  label: z.string(),
  icon: z.string(),
  description: z.string().optional(),
});

// Agent summary schema that maps to the preview fields
export const agentSummarySchema = z.object({
  name: z.string().min(1, "Agent name is required"),
  description: z.string().min(1, "Agent description is required"),
  inputs: z.array(agentInputSchema).min(1, "At least one input is required"),
  outputs: z.array(agentOutputSchema).min(1, "At least one output is required"),
  process: z.string().min(1, "Process description is required"),
});

// Structured response schema for agent builder
export const agentBuilderResponseSchema = z.object({
  message: z.string().min(1, "Message is required"),
  agentSummary: agentSummarySchema,
});

// Type exports
export type AgentInput = z.infer<typeof agentInputSchema>;
export type AgentOutput = z.infer<typeof agentOutputSchema>;
export type AgentSummary = z.infer<typeof agentSummarySchema>;
export type AgentBuilderResponse = z.infer<typeof agentBuilderResponseSchema>;

// Validation helper function
export const validateAgentBuilderResponse = (
  data: unknown
): AgentBuilderResponse => {
  return agentBuilderResponseSchema.parse(data);
};
