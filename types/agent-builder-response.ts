import { z } from "zod";

// Base schemas for agent components
export const agentInputSchema = z.object({
  id: z.string(),
  type: z.string(),
  label: z.string(),
  icon: z.string(),
  description: z.string().optional(),
});

export const agentOutputSchema = z.object({
  id: z.string(),
  type: z.string(),
  label: z.string(),
  icon: z.string(),
  description: z.string().optional(),
});

// Agent summary schema that maps to the preview fields
export const agentSummarySchema = z.object({
  name: z.string().min(1, "Agent name is required"),
  description: z.string().min(1, "Agent description is required"),
  inputs: z.array(agentInputSchema).min(1, "At least one input is required"),
  outputs: z.array(agentOutputSchema).min(1, "At least one output is required"),
  process: z.string().min(1, "Process description is required"),
  version: z.string().optional().default("v1"),
});

// Structured response schema for agent builder
export const agentBuilderResponseSchema = z.object({
  message: z.string().min(1, "Message is required"),
  agentSummary: agentSummarySchema,
  reasoning: z.string().optional(), // Optional field for AI reasoning
  suggestions: z.array(z.string()).optional(), // Optional suggestions for improvement
});

// Schema for iterative updates (when modifying existing agent)
export const agentUpdateResponseSchema = z.object({
  message: z.string().min(1, "Message is required"),
  agentSummary: agentSummarySchema,
  changes: z.array(z.object({
    field: z.string(),
    oldValue: z.string().optional(),
    newValue: z.string(),
    reason: z.string(),
  })).optional(),
  reasoning: z.string().optional(),
  suggestions: z.array(z.string()).optional(),
});

// Union type for all possible agent builder responses
export const agentBuilderUnionResponseSchema = z.union([
  agentBuilderResponseSchema,
  agentUpdateResponseSchema,
]);

// Type exports
export type AgentInput = z.infer<typeof agentInputSchema>;
export type AgentOutput = z.infer<typeof agentOutputSchema>;
export type AgentSummary = z.infer<typeof agentSummarySchema>;
export type AgentBuilderResponse = z.infer<typeof agentBuilderResponseSchema>;
export type AgentUpdateResponse = z.infer<typeof agentUpdateResponseSchema>;
export type AgentBuilderUnionResponse = z.infer<typeof agentBuilderUnionResponseSchema>;

// Validation helper functions
export const validateAgentBuilderResponse = (data: unknown): AgentBuilderResponse => {
  return agentBuilderResponseSchema.parse(data);
};

export const validateAgentUpdateResponse = (data: unknown): AgentUpdateResponse => {
  return agentUpdateResponseSchema.parse(data);
};

export const validateAgentBuilderUnionResponse = (data: unknown): AgentBuilderUnionResponse => {
  return agentBuilderUnionResponseSchema.parse(data);
};

// Default values for new agents
export const defaultAgentSummary: AgentSummary = {
  name: "New AI Agent",
  description: "A helpful AI agent",
  inputs: [
    {
      id: "1",
      type: "text",
      label: "user input",
      icon: "message-square",
      description: "Text input from the user",
    },
  ],
  outputs: [
    {
      id: "1",
      type: "text",
      label: "response",
      icon: "message-circle",
      description: "AI generated response",
    },
  ],
  process: "Process user input and generate a helpful response",
  version: "v1",
};
