import { z } from "zod";
import { generateObject, generateText } from "ai";
import { openai } from "@ai-sdk/openai";
import { router } from "../trpc";
import { buildProcedure } from "../trpc";
import { Ratelimit } from "@upstash/ratelimit";
import { redis } from "@/utils/redis/client";
import {
  agentBuilderResponseSchema,
  agentSummarySchema,
  validateAgentBuilderResponse,
} from "@/types/agent-builder-response";

// Create a rate limiter for agent builder requests (3 requests per minute)
const agentBuilderRateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(3, "1 m"),
  analytics: true,
});

// Input schema for agent builder messages
const agentBuilderInputSchema = z.object({
  messages: z.array(
    z.object({
      id: z.string(),
      role: z.enum(["user", "assistant"]),
      content: z.string(),
      timestamp: z.string().optional(),
    })
  ),
});

// Input schema for solution generation
const generateSolutionInputSchema = z.object({
  agentSummary: agentSummarySchema,
  versionId: z.string(),
});

// System prompt for agent creation
const AGENT_CREATION_SYSTEM_PROMPT = `You are an AI agent builder assistant. Your role is to help users create AI agent configurations.

When a user describes what they want their agent to do, you should:
1. Analyze their requirements carefully
2. Create a structured agent configuration with appropriate inputs, outputs, and process
3. Provide a helpful message explaining what you've created

Guidelines for creating agents:
- Inputs should represent what the user will provide to the agent
- Outputs should represent what the agent will generate or produce
- Process should be a clear, actionable description of how the agent transforms inputs to outputs
- Use descriptive but concise labels
- Choose appropriate icons from lucide-react (e.g., message-square, file-text, database, code, bar-chart)

Common input/output types:
- text, file, image, audio, video (content types)
- database_schema, api_endpoint, json_data (data types)
- python_script, javascript_code, sql_query (code types)
- chart, report, analysis, summary (output types)

Always respond with valid JSON matching the required schema. Be creative but practical.`;

export const agentBuilderRouter = router({
  generateAgent: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(agentBuilderInputSchema)
    .mutation(async ({ input }) => {
      const { messages } = input;

      try {
        // Generate structured response
        const result = await generateObject({
          model: openai("gpt-4o-mini"),
          system: AGENT_CREATION_SYSTEM_PROMPT,
          messages: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          schema: agentBuilderResponseSchema,
          temperature: 0.7,
        });

        // Validate response
        const validatedResponse = validateAgentBuilderResponse(result.object);

        return {
          success: true,
          data: validatedResponse,
        };
      } catch (error) {
        console.error("Agent generation error:", error);

        // Return fallback response with minimal agent
        const fallbackResponse = {
          message:
            "I encountered an error while processing your request. Please try again with a different description.",
          agentSummary: {
            name: "Simple Agent",
            description: "A basic AI agent",
            inputs: [
              {
                id: "1",
                type: "text",
                label: "user input",
                icon: "message-square",
              },
            ],
            outputs: [
              {
                id: "1",
                type: "text",
                label: "response",
                icon: "message-square",
              },
            ],
            process: "Process user input and generate a response",
          },
        };

        return {
          success: true,
          data: fallbackResponse,
          fallback: true,
        };
      }
    }),

  generateSolution: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(generateSolutionInputSchema)
    .mutation(async ({ input }) => {
      const { agentSummary, versionId } = input;

      try {
        const promptToCreateAgentPrompt = `You are an expert prompt writer. Your task is to create a prompt that spins up an AI agent to help guide the user through automating a process.
Instructions for prompt creation:
1. The prompt should start by establishing the AI agent's identity and expertise
2. The prompt should instruct the agent to ask the user for each inputs; while asking for inputs the agent should provide context and examples.
3. The prompt should instruct the agent to continue to ask questions until all of the necessary context is gathered
4. The prompt should instruct the agent to proceed with the process after collecting ALL required inputs
5. The prompt should be detailed and step-by-step, an SOP for an agent to follow
6. The prompt should contain details about the output format, which should be clearly specified with a full example
7. The prompt should be conversational and guide the user through the entire workflow
`;

        // Generate solution using AI
        const result = await generateText({
          model: openai("gpt-4o-mini"),
          system: promptToCreateAgentPrompt,
          prompt: `Agent Configuration:
Name: ${agentSummary.name}
Description: ${agentSummary.description}
Process: ${agentSummary.process}

Required Inputs (the agent must ask for these):
${agentSummary.inputs.map((input) => `- ${input.label} (${input.type}): ${input.description || "No description provided"}`).join("\n")}

Expected Outputs (the agent must generate these):
${agentSummary.outputs.map((output) => `- ${output.label} (${output.type}): ${output.description || "No description provided"}`).join("\n")}
`,
          temperature: 0.7,
        });

        return {
          success: true,
          solution: {
            id: `solution-${versionId}-${Date.now()}`,
            name: `${agentSummary.name} Solution`,
            description: `Ready-to-use ChatGPT prompt for ${agentSummary.description}`,
            prompt: result.text,
            inputs: agentSummary.inputs.map((input) => input.label),
            outputs: agentSummary.outputs.map((output) => output.label),
          },
        };
      } catch (error) {
        console.error("Solution generation error:", error);

        return {
          success: false,
          error: "Failed to generate solution. Please try again.",
        };
      }
    }),
});
