import { z } from "zod";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { router } from "../trpc";
import { buildProcedure } from "../trpc";
import { Ratelimit } from "@upstash/ratelimit";
import { redis } from "@/utils/redis/client";
import { 
  agentBuilderResponseSchema, 
  agentUpdateResponseSchema,
  agentSummarySchema,
  validateAgentBuilderResponse,
  validateAgentUpdateResponse,
  defaultAgentSummary
} from "@/types/agent-builder-response";

// Create a rate limiter for agent builder requests (3 requests per minute)
const agentBuilderRateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(3, "1 m"),
  analytics: true,
});

// Input schema for agent builder messages
const agentBuilderInputSchema = z.object({
  messages: z.array(
    z.object({
      id: z.string(),
      role: z.enum(["user", "assistant"]),
      content: z.string(),
      timestamp: z.string().optional(),
    })
  ),
  currentAgentSummary: agentSummarySchema.optional(),
});

// System prompts
const AGENT_CREATION_SYSTEM_PROMPT = `You are an AI agent builder assistant. Your role is to help users create and iterate on AI agent configurations.

When a user describes what they want their agent to do, you should:
1. Analyze their requirements carefully
2. Create a structured agent configuration with appropriate inputs, outputs, and process
3. Provide a helpful message explaining what you've created
4. Suggest improvements or ask clarifying questions if needed

Guidelines for creating agents:
- Inputs should represent what the user will provide to the agent
- Outputs should represent what the agent will generate or produce
- Process should be a clear, actionable description of how the agent transforms inputs to outputs
- Use descriptive but concise labels
- Choose appropriate icons from lucide-react (e.g., message-square, file-text, database, code, bar-chart)

Common input/output types:
- text, file, image, audio, video (content types)
- database_schema, api_endpoint, json_data (data types)  
- python_script, javascript_code, sql_query (code types)
- chart, report, analysis, summary (output types)

Always respond with valid JSON matching the required schema. Be creative but practical.`;

const AGENT_ITERATION_SYSTEM_PROMPT = `You are an AI agent builder assistant helping users iterate on existing agent configurations.

The user has an existing agent and wants to modify it. You should:
1. Understand what changes they want to make
2. Update the agent configuration appropriately  
3. Explain what changes you made and why
4. Track changes in the changes array
5. Provide suggestions for further improvements

When making changes:
- Maintain consistency with the agent's core purpose
- Ensure inputs and outputs still work together logically
- Keep the process description clear and actionable
- Be thoughtful about additions vs modifications vs removals

Always respond with valid JSON and document your changes clearly.`;

export const agentBuilderRouter = router({
  generateAgent: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(agentBuilderInputSchema)
    .mutation(async ({ input }) => {
      const { messages, currentAgentSummary } = input;

      // Determine if this is iteration or creation
      const isIteration = currentAgentSummary && Object.keys(currentAgentSummary).length > 0;
      
      // Prepare system prompt with context
      const systemPrompt = isIteration 
        ? AGENT_ITERATION_SYSTEM_PROMPT 
        : AGENT_CREATION_SYSTEM_PROMPT;

      const contextualSystemPrompt = isIteration
        ? `${systemPrompt}\n\nCurrent agent configuration:\n${JSON.stringify(currentAgentSummary, null, 2)}`
        : systemPrompt;

      // Use appropriate schema
      const schema = isIteration ? agentUpdateResponseSchema : agentBuilderResponseSchema;

      try {
        // Generate structured response
        const result = await generateObject({
          model: openai("gpt-4o-mini"),
          system: contextualSystemPrompt,
          messages: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          schema,
          temperature: 0.7,
        });

        // Validate response
        const validatedResponse = isIteration 
          ? validateAgentUpdateResponse(result.object)
          : validateAgentBuilderResponse(result.object);

        return {
          success: true,
          data: validatedResponse,
          isIteration,
        };

      } catch (error) {
        console.error("Agent generation error:", error);
        
        // Return fallback response
        const fallbackResponse = {
          message: "I encountered an error while processing your request. Here's a basic agent configuration to get you started.",
          agentSummary: currentAgentSummary || defaultAgentSummary,
          reasoning: "Fallback response due to processing error",
        };

        return {
          success: true,
          data: fallbackResponse,
          isIteration: false,
          fallback: true,
        };
      }
    }),

  validateAgentSummary: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(z.unknown())
    .query(({ input }) => {
      try {
        const validatedSummary = agentSummarySchema.parse(input);
        return {
          valid: true,
          data: validatedSummary,
        };
      } catch (error) {
        return {
          valid: false,
          error: error instanceof Error ? error.message : "Validation failed",
        };
      }
    }),
});
