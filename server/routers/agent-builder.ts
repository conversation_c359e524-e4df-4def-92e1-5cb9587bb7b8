import { z } from "zod";
import { generateObject, generateText } from "ai";
import { openai } from "@ai-sdk/openai";
import { router } from "../trpc";
import { buildProcedure } from "../trpc";
import { Ratelimit } from "@upstash/ratelimit";
import { redis } from "@/utils/redis/client";
import {
  agentBuilderResponseSchema,
  agentSummarySchema,
  validateAgentBuilderResponse,
} from "@/types/agent-builder-response";

// Create a rate limiter for agent builder requests (3 requests per minute)
const agentBuilderRateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(3, "1 m"),
  analytics: true,
});

// Input schema for agent builder messages
const agentBuilderInputSchema = z.object({
  messages: z.array(
    z.object({
      id: z.string(),
      role: z.enum(["user", "assistant"]),
      content: z.string(),
      timestamp: z.string().optional(),
    })
  ),
  previousSop: z.string().optional(), // Previous SOP if exists
});

// Input schema for solution generation
const generateSolutionInputSchema = z.object({
  agentSummary: agentSummarySchema,
  versionId: z.string(),
  sop: z.string(), // Required SOP for solution generation
  chatContext: z.string().optional(), // Chat history for context
});

// System prompt for agent creation
const AGENT_CREATION_SYSTEM_PROMPT = `You are an AI agent builder assistant. Your role is to help users create AI agent configurations.

When a user describes what they want their agent to do, you should:
1. Analyze their requirements carefully
2. Create a structured agent configuration with appropriate inputs, outputs, and process
3. Provide a helpful message explaining what you've created

Guidelines for creating agents:
- Inputs should represent what the user will provide to the agent
- Outputs should represent what the agent will generate or produce
- Process should be a clear, actionable description of how the agent transforms inputs to outputs
- Use descriptive but concise labels
- Choose appropriate icons from lucide-react (e.g., message-square, file-text, database, code, bar-chart)

Common input/output types:
- text, file, image, audio, video (content types)
- database_schema, api_endpoint, json_data (data types)
- python_script, javascript_code, sql_query (code types)
- chart, report, analysis, summary (output types)

Always respond with valid JSON matching the required schema. Be creative but practical.`;

// System prompt for generating Agent SOP
const AGENT_SOP_GENERATION_PROMPT = `You are an AI that creates Standard Operating Procedures (SOPs) for AI agents.

Your task is to create a detailed, step-by-step SOP that an AI agent will follow to accomplish a specific task. The SOP should be clear, actionable, and comprehensive.

CRITICAL CONSTRAINTS:
- You MUST ONLY ask for the inputs that are explicitly defined in the agent configuration
- Do NOT ask for additional information beyond the specified inputs
- Each input collection step should correspond exactly to one defined input
- Do not add extra steps for gathering information not in the input list

Structure your SOP as follows:
1. Start with a clear title that describes what the agent will accomplish
2. For each defined input, create ONE step that asks for that specific input
3. Include the process execution steps
4. Include output generation steps
5. Include any follow-up or iteration steps

Guidelines:
- Only collect the inputs that are explicitly defined in the agent configuration
- Provide examples and context when asking for each defined input
- Specify the format and structure of outputs
- Include validation steps for the defined inputs only
- Make the SOP comprehensive but constrained to the defined scope

Generate only the SOP - no additional formatting or explanation.

Example Agent SOP
---

Title: Generate personalized cold email draft
1. Ask the user to provide a URL for a business they want to write a cold email to. Provide the user with context and an example. Interact with them until they provide the URL.
2. Ask the user to provide a description of their product. Provide the user with context and an example. Interact with them until they provide all of the necessary context.
3. Use your search tool to research the business via its URL and its industry
4. Generate a cold email personalized to this business for why they would want to use the provided product. Ask them if they'd like a demo
5. Ask the user if they'd like to make any changes to the email. Repeat steps 4-5 forever`;

// System prompt for converting SOP to ChatGPT prompt
const SOP_TO_PROMPT_CONVERSION = `You are an AI that converts Agent SOPs into ready-to-use ChatGPT prompts.

Your task is to take a Standard Operating Procedure (SOP) and wrap it in a complete ChatGPT prompt that:
1. Establishes the AI agent's identity and domain expertise
2. Explains the agent's purpose and capabilities
3. Instructs the agent to follow the provided SOP
4. Includes relevant context from the conversation that created the agent
5. Sets the right tone and interaction style

Structure the prompt as follows:
- Start with "You are [agent identity and expertise]..."
- Explain the agent's purpose and what it helps users accomplish
- Include relevant context from the conversation (if provided)
- Present the SOP as the process the agent should follow
- Include any additional instructions about tone, style, or behavior
- End with instructions to begin the process

Guidelines:
- Make the agent identity relevant to the task
- Establish appropriate domain expertise
- Include conversation context that would be helpful for the agent
- Keep the tone professional but friendly
- Ensure the agent understands to follow the SOP step-by-step
- Include instructions to be helpful and thorough

Generate only the complete ChatGPT prompt - no additional formatting or explanation.`;

export const agentBuilderRouter = router({
  generateAgent: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(agentBuilderInputSchema)
    .mutation(async ({ input }) => {
      const { messages, previousSop } = input;

      try {
        // Step 1: Generate Agent Summary
        const result = await generateObject({
          model: openai("gpt-4o-mini"),
          system: AGENT_CREATION_SYSTEM_PROMPT,
          messages: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          schema: agentBuilderResponseSchema,
          temperature: 0.7,
        });

        // Validate response
        const validatedResponse = validateAgentBuilderResponse(result.object);

        // Step 2: Generate Agent SOP
        const chatHistory = messages
          .map((msg) => `${msg.role}: ${msg.content}`)
          .join("\n");

        const sopContext = `Chat History:
${chatHistory}

Agent Summary:
Name: ${validatedResponse.agentSummary.name}
Description: ${validatedResponse.agentSummary.description}
Process: ${validatedResponse.agentSummary.process}

REQUIRED INPUTS (You must ONLY ask for these specific inputs, nothing else):
${validatedResponse.agentSummary.inputs.map((input) => `- ${input.label} (${input.type}): ${input.description || "No description provided"}`).join("\n")}

Expected Outputs:
${validatedResponse.agentSummary.outputs.map((output) => `- ${output.label} (${output.type}): ${output.description || "No description provided"}`).join("\n")}

IMPORTANT: The SOP should only ask the user for the inputs listed above. Do not ask for any additional information like names, contact details, or other data not explicitly defined in the inputs.

${
  previousSop
    ? `Previous SOP:
${previousSop}

Please update and improve the SOP based on the new chat context and agent summary, but maintain the constraint of only asking for the defined inputs.`
    : "Create a detailed SOP for this AI agent that strictly follows the input constraints."
}`;

        const sopResult = await generateText({
          model: openai("gpt-4o-mini"),
          system: AGENT_SOP_GENERATION_PROMPT,
          prompt: sopContext,
          temperature: 0.7,
        });

        // Add SOP to the response
        const responseWithSop = {
          ...validatedResponse,
          agentSummary: {
            ...validatedResponse.agentSummary,
            sop: sopResult.text,
          },
        };

        return {
          success: true,
          data: responseWithSop,
        };
      } catch (error) {
        console.error("Agent generation error:", error);

        // Return fallback response with minimal agent
        const fallbackResponse = {
          message:
            "I encountered an error while processing your request. Please try again with a different description.",
          agentSummary: {
            name: "Simple Agent",
            description: "A basic AI agent",
            inputs: [
              {
                id: "1",
                type: "text",
                label: "user input",
                icon: "message-square",
              },
            ],
            outputs: [
              {
                id: "1",
                type: "text",
                label: "response",
                icon: "message-square",
              },
            ],
            process: "Process user input and generate a response",
            sop: "Title: Basic AI Assistant\n1. Ask the user what they need help with\n2. Process their request\n3. Provide a helpful response",
          },
        };

        return {
          success: true,
          data: fallbackResponse,
          fallback: true,
        };
      }
    }),

  generateSolution: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(generateSolutionInputSchema)
    .mutation(async ({ input }) => {
      const { agentSummary, versionId, sop, chatContext } = input;

      try {
        // Convert SOP to ChatGPT prompt
        const promptContext = `Agent Information:
Name: ${agentSummary.name}
Description: ${agentSummary.description}
Domain: ${agentSummary.process}

${
  chatContext
    ? `Conversation Context:
${chatContext}

`
    : ""
}Agent SOP:
${sop}

Convert this SOP into a complete ChatGPT prompt that establishes the agent's identity and instructs it to follow the SOP. ${chatContext ? "Include relevant context from the conversation to help the agent understand the specific use case." : ""}`;

        const promptResult = await generateText({
          model: openai("gpt-4o-mini"),
          system: SOP_TO_PROMPT_CONVERSION,
          prompt: promptContext,
          temperature: 0.7,
        });

        return {
          success: true,
          solution: {
            id: `solution-${versionId}-${Date.now()}`,
            name: `${agentSummary.name} Solution`,
            description: `Ready-to-use ChatGPT prompt for ${agentSummary.description}`,
            prompt: promptResult.text,
            inputs: agentSummary.inputs.map((input) => input.label),
            outputs: agentSummary.outputs.map((output) => output.label),
          },
        };
      } catch (error) {
        console.error("Solution generation error:", error);

        return {
          success: false,
          error: "Failed to generate solution. Please try again.",
        };
      }
    }),
});
