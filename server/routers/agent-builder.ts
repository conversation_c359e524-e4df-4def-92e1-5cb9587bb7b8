import { z } from "zod";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { router } from "../trpc";
import { buildProcedure } from "../trpc";
import { Ratelimit } from "@upstash/ratelimit";
import { redis } from "@/utils/redis/client";
import {
  agentBuilderResponseSchema,
  agentSummarySchema,
  validateAgentBuilderResponse,
} from "@/types/agent-builder-response";

// Create a rate limiter for agent builder requests (3 requests per minute)
const agentBuilderRateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(3, "1 m"),
  analytics: true,
});

// Input schema for agent builder messages
const agentBuilderInputSchema = z.object({
  messages: z.array(
    z.object({
      id: z.string(),
      role: z.enum(["user", "assistant"]),
      content: z.string(),
      timestamp: z.string().optional(),
    })
  ),
});

// Input schema for solution generation
const generateSolutionInputSchema = z.object({
  agentSummary: agentSummarySchema,
  versionId: z.string(),
});

// Output schema for solution generation
const solutionResponseSchema = z.object({
  name: z.string(),
  description: z.string(),
  prompt: z.string(),
  inputs: z.array(z.string()),
  outputs: z.array(z.string()),
});

// System prompt for agent creation
const AGENT_CREATION_SYSTEM_PROMPT = `You are an AI agent builder assistant. Your role is to help users create AI agent configurations.

When a user describes what they want their agent to do, you should:
1. Analyze their requirements carefully
2. Create a structured agent configuration with appropriate inputs, outputs, and process
3. Provide a helpful message explaining what you've created

Guidelines for creating agents:
- Inputs should represent what the user will provide to the agent
- Outputs should represent what the agent will generate or produce
- Process should be a clear, actionable description of how the agent transforms inputs to outputs
- Use descriptive but concise labels
- Choose appropriate icons from lucide-react (e.g., message-square, file-text, database, code, bar-chart)

Common input/output types:
- text, file, image, audio, video (content types)
- database_schema, api_endpoint, json_data (data types)
- python_script, javascript_code, sql_query (code types)
- chart, report, analysis, summary (output types)

Always respond with valid JSON matching the required schema. Be creative but practical.`;

// System prompt for solution generation
const SOLUTION_GENERATION_SYSTEM_PROMPT = `You are an AI solution generator. Your role is to create ready-to-use prompts based on agent configurations.

Given an agent configuration with inputs, outputs, and process, you should:
1. Create a comprehensive prompt that implements the agent's functionality
2. Generate a clear name and description for the solution
3. List the specific inputs and outputs the prompt expects

Guidelines for solution generation:
- The prompt should be self-contained and ready to use
- Include clear instructions for how to use the inputs
- Specify the expected output format
- Make the prompt conversational and easy to understand
- Include examples or placeholders where helpful

The solution should be practical and immediately usable by someone who wants to implement this agent's functionality.

Respond with a JSON object containing:
- name: A clear, descriptive name for the solution
- description: A brief description of what the solution does
- prompt: The complete, ready-to-use prompt
- inputs: Array of input names/types expected by the prompt
- outputs: Array of output names/types the prompt will generate`;

export const agentBuilderRouter = router({
  generateAgent: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(agentBuilderInputSchema)
    .mutation(async ({ input }) => {
      const { messages } = input;

      try {
        // Generate structured response
        const result = await generateObject({
          model: openai("gpt-4o-mini"),
          system: AGENT_CREATION_SYSTEM_PROMPT,
          messages: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          schema: agentBuilderResponseSchema,
          temperature: 0.7,
        });

        // Validate response
        const validatedResponse = validateAgentBuilderResponse(result.object);

        return {
          success: true,
          data: validatedResponse,
        };
      } catch (error) {
        console.error("Agent generation error:", error);

        // Return fallback response with minimal agent
        const fallbackResponse = {
          message:
            "I encountered an error while processing your request. Please try again with a different description.",
          agentSummary: {
            name: "Simple Agent",
            description: "A basic AI agent",
            inputs: [
              {
                id: "1",
                type: "text",
                label: "user input",
                icon: "message-square",
              },
            ],
            outputs: [
              {
                id: "1",
                type: "text",
                label: "response",
                icon: "message-square",
              },
            ],
            process: "Process user input and generate a response",
          },
        };

        return {
          success: true,
          data: fallbackResponse,
          fallback: true,
        };
      }
    }),

  generateSolution: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(generateSolutionInputSchema)
    .mutation(async ({ input }) => {
      const { agentSummary, versionId } = input;

      try {
        // Create a detailed prompt for solution generation
        const agentContext = `Agent Configuration:
Name: ${agentSummary.name}
Description: ${agentSummary.description}
Process: ${agentSummary.process}

Inputs:
${agentSummary.inputs.map((input) => `- ${input.label} (${input.type}): ${input.description || "No description"}`).join("\n")}

Outputs:
${agentSummary.outputs.map((output) => `- ${output.label} (${output.type}): ${output.description || "No description"}`).join("\n")}`;

        // Generate solution using AI
        const result = await generateObject({
          model: openai("gpt-4o-mini"),
          system: SOLUTION_GENERATION_SYSTEM_PROMPT,
          prompt: `Create a ready-to-use solution for this agent:\n\n${agentContext}`,
          schema: solutionResponseSchema,
          temperature: 0.7,
        });

        return {
          success: true,
          solution: {
            id: `solution-${versionId}-${Date.now()}`,
            ...result.object,
          },
        };
      } catch (error) {
        console.error("Solution generation error:", error);

        // Return fallback solution
        const fallbackSolution = {
          id: `solution-${versionId}-fallback`,
          name: `${agentSummary.name} Solution`,
          description: `A solution for ${agentSummary.description}`,
          prompt: `You are an AI assistant that helps with ${agentSummary.description.toLowerCase()}.

Process: ${agentSummary.process}

Please provide helpful and accurate responses based on the user's input.`,
          inputs: agentSummary.inputs.map((input) => input.label),
          outputs: agentSummary.outputs.map((output) => output.label),
        };

        return {
          success: true,
          solution: fallbackSolution,
          fallback: true,
        };
      }
    }),
});
