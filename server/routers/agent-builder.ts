import { z } from "zod";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { router } from "../trpc";
import { buildProcedure } from "../trpc";
import { Ratelimit } from "@upstash/ratelimit";
import { redis } from "@/utils/redis/client";
import {
  agentBuilderResponseSchema,
  agentSummarySchema,
  validateAgentBuilderResponse,
} from "@/types/agent-builder-response";

// Create a rate limiter for agent builder requests (3 requests per minute)
const agentBuilderRateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(3, "1 m"),
  analytics: true,
});

// Input schema for agent builder messages
const agentBuilderInputSchema = z.object({
  messages: z.array(
    z.object({
      id: z.string(),
      role: z.enum(["user", "assistant"]),
      content: z.string(),
      timestamp: z.string().optional(),
    })
  ),
});

// Input schema for solution generation
const generateSolutionInputSchema = z.object({
  agentSummary: agentSummarySchema,
  versionId: z.string(),
});

// Output schema for solution generation
const solutionResponseSchema = z.object({
  name: z.string(),
  description: z.string(),
  prompt: z.string(),
  inputs: z.array(z.string()),
  outputs: z.array(z.string()),
});

// System prompt for agent creation
const AGENT_CREATION_SYSTEM_PROMPT = `You are an AI agent builder assistant. Your role is to help users create AI agent configurations.

When a user describes what they want their agent to do, you should:
1. Analyze their requirements carefully
2. Create a structured agent configuration with appropriate inputs, outputs, and process
3. Provide a helpful message explaining what you've created

Guidelines for creating agents:
- Inputs should represent what the user will provide to the agent
- Outputs should represent what the agent will generate or produce
- Process should be a clear, actionable description of how the agent transforms inputs to outputs
- Use descriptive but concise labels
- Choose appropriate icons from lucide-react (e.g., message-square, file-text, database, code, bar-chart)

Common input/output types:
- text, file, image, audio, video (content types)
- database_schema, api_endpoint, json_data (data types)
- python_script, javascript_code, sql_query (code types)
- chart, report, analysis, summary (output types)

Always respond with valid JSON matching the required schema. Be creative but practical.`;

// System prompt for solution generation
const SOLUTION_GENERATION_SYSTEM_PROMPT = `You are an AI solution generator that creates ready-to-use ChatGPT prompts based on agent configurations.

Your task is to create a prompt that will be pasted into ChatGPT to spin up an AI agent. The prompt should follow this structure:

1. **Agent Identity**: Define the AI agent's role and expertise
2. **Input Collection**: Instruct the agent to ask the user for required inputs with:
   - Clear questions for each input
   - Example inputs to guide the user
   - Context about why each input is needed
3. **Process Execution**: Detailed instructions on how to execute the process after collecting inputs
4. **Output Generation**: Specific instructions on output format and structure with examples

Guidelines for the generated prompt:
- Start with "You are [agent role/identity]..."
- For each input, create a specific question with example and context
- Include step-by-step process instructions
- Provide detailed output format specifications with examples
- Make the prompt conversational and user-friendly
- Ensure the agent asks for ALL inputs before proceeding
- Include validation or clarification steps if needed

The resulting prompt should be complete and ready to paste into ChatGPT.

Respond with a JSON object containing:
- name: A clear, descriptive name for the solution
- description: A brief description of what the solution does
- prompt: The complete, ready-to-use ChatGPT prompt
- inputs: Array of input names/types expected by the prompt
- outputs: Array of output names/types the prompt will generate`;

export const agentBuilderRouter = router({
  generateAgent: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(agentBuilderInputSchema)
    .mutation(async ({ input }) => {
      const { messages } = input;

      try {
        // Generate structured response
        const result = await generateObject({
          model: openai("gpt-4o-mini"),
          system: AGENT_CREATION_SYSTEM_PROMPT,
          messages: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          schema: agentBuilderResponseSchema,
          temperature: 0.7,
        });

        // Validate response
        const validatedResponse = validateAgentBuilderResponse(result.object);

        return {
          success: true,
          data: validatedResponse,
        };
      } catch (error) {
        console.error("Agent generation error:", error);

        // Return fallback response with minimal agent
        const fallbackResponse = {
          message:
            "I encountered an error while processing your request. Please try again with a different description.",
          agentSummary: {
            name: "Simple Agent",
            description: "A basic AI agent",
            inputs: [
              {
                id: "1",
                type: "text",
                label: "user input",
                icon: "message-square",
              },
            ],
            outputs: [
              {
                id: "1",
                type: "text",
                label: "response",
                icon: "message-square",
              },
            ],
            process: "Process user input and generate a response",
          },
        };

        return {
          success: true,
          data: fallbackResponse,
          fallback: true,
        };
      }
    }),

  generateSolution: buildProcedure({
    type: "public",
    rateLimit: agentBuilderRateLimiter,
  })
    .input(generateSolutionInputSchema)
    .mutation(async ({ input }) => {
      const { agentSummary, versionId } = input;

      try {
        // Create a detailed prompt for solution generation
        const agentContext = `Agent Configuration:
Name: ${agentSummary.name}
Description: ${agentSummary.description}
Process: ${agentSummary.process}

Required Inputs (the agent must ask for these):
${agentSummary.inputs.map((input) => `- ${input.label} (${input.type}): ${input.description || "No description provided"}`).join("\n")}

Expected Outputs (the agent must generate these):
${agentSummary.outputs.map((output) => `- ${output.label} (${output.type}): ${output.description || "No description provided"}`).join("\n")}

Instructions for prompt creation:
1. The prompt should start by establishing the AI agent's identity and expertise
2. The agent should ask the user for each input with clear questions, examples, and context
3. The agent should only proceed with the process after collecting ALL required inputs
4. The process instructions should be detailed and step-by-step
5. Output format should be clearly specified with examples
6. The prompt should be conversational and guide the user through the entire workflow`;

        // Generate solution using AI
        const result = await generateObject({
          model: openai("gpt-4o-mini"),
          system: SOLUTION_GENERATION_SYSTEM_PROMPT,
          prompt: `Create a comprehensive ChatGPT prompt for this agent:\n\n${agentContext}`,
          schema: solutionResponseSchema,
          temperature: 0.7,
        });

        return {
          success: true,
          solution: {
            id: `solution-${versionId}-${Date.now()}`,
            ...result.object,
          },
        };
      } catch (error) {
        console.error("Solution generation error:", error);

        // Return fallback solution
        const inputQuestions = agentSummary.inputs
          .map(
            (input) =>
              `Please provide your ${input.label} (${input.type}): ${input.description || "No description available"}`
          )
          .join("\n");

        const outputFormat = agentSummary.outputs
          .map(
            (output) =>
              `- ${output.label}: ${output.description || "No description available"}`
          )
          .join("\n");

        const fallbackSolution = {
          id: `solution-${versionId}-fallback`,
          name: `${agentSummary.name} Solution`,
          description: `A solution for ${agentSummary.description}`,
          prompt: `You are an AI agent specialized in ${agentSummary.description.toLowerCase()}.

Your task is to help users by following this process:

1. **Input Collection**: First, ask the user for the following information:
${inputQuestions}

2. **Process Execution**: Once you have all the required inputs, execute this process:
${agentSummary.process}

3. **Output Generation**: Provide your response in the following format:
${outputFormat}

Please start by asking for the required inputs. Only proceed with the process after you have collected all necessary information from the user.`,
          inputs: agentSummary.inputs.map((input) => input.label),
          outputs: agentSummary.outputs.map((output) => output.label),
        };

        return {
          success: true,
          solution: fallbackSolution,
          fallback: true,
        };
      }
    }),
});
