import { router } from "../trpc";
import { exampleRouter } from "./example";
import { leadRouter } from "./lead";
import { onboardingRouter } from "./onboarding";
import { stripeRouter } from "./stripe";
import { contentRouter } from "./content";
import { searchRouter } from "./search";
import { analyticsRouter } from "./analytics";
import { collectionsRouter } from "./collections";
import { chatRouter } from "./chat";

export const appRouter = router({
  example: exampleRouter,
  lead: leadRouter,
  stripe: stripeRouter,
  onboarding: onboardingRouter,
  content: contentRouter,
  search: searchRouter,
  analytics: analyticsRouter,
  collections: collectionsRouter,
  chat: chatRouter,
});

// Export type definition of API
export type AppRouter = typeof appRouter;
