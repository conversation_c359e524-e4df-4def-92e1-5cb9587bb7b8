{"name": "saas-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:watch": "nodemon --watch . --ext js,jsx,ts,tsx --exec \"pnpm run build\"", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "import-tasks": "tsx scripts/import-tasks.ts", "sync-tasks-to-pinecone": "tsx scripts/sync-tasks-to-pinecone.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/react": "^1.2.12", "@auth/mongodb-adapter": "^1.0.0", "@headlessui/react": "^1.7.19", "@heroicons/react": "^2.2.0", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@next/mdx": "^13.5.7", "@pinecone-database/pinecone": "^6.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.4", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^4.36.1", "@trpc/client": "^10.45.2", "@trpc/next": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@upstash/ratelimit": "^2.0.4", "@upstash/redis": "^1.34.3", "ai": "^4.3.19", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^14.0.0", "crisp-sdk-web": "^1.0.25", "dotenv": "^17.0.0", "eslint": "8.47.0", "eslint-config-next": "13.4.19", "form-data": "^4.0.1", "lucide-react": "^0.462.0", "mongodb": "^5.9.2", "mongoose": "^7.8.2", "next": "^14.2.18", "next-auth": "^4.24.10", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "nextjs-toploader": "^1.6.12", "nodemailer": "^6.9.16", "openai": "^5.8.2", "posthog-js": "^1.256.0", "posthog-node": "^5.1.1", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "^5.28.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "resend": "^4.0.1", "stripe": "^13.11.0", "superjson": "^2.2.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "zod": "^3.23.8", "zustand": "^5.0.6"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/mdx": "^2.0.13", "@types/mongoose": "^5.11.97", "@types/node": "^20.17.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "autoprefixer": "^10.4.20", "daisyui": "^4.12.14", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "nodemon": "^3.1.9", "postcss": "^8.4.49", "prettier": "^3.4.1", "tailwindcss": "^3.4.15", "typescript": "^5.6.3"}}