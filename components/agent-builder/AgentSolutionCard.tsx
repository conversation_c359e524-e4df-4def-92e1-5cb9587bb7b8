"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, <PERSON>rk<PERSON> } from "lucide-react";
import { AgentSummary } from "@/types/agent-builder-response";
import { trpc } from "@/utils/trpc/client";
import { AIPromptButton } from "@/components/ai-prompt-button";
import { useAgentBuilderStore } from "@/stores/agentBuilderStore";

interface AgentSolutionCardProps {
  currentAgent: AgentSummary;
  currentVersionId: string | null;
}

export function AgentSolutionCard({
  currentAgent,
  currentVersionId,
}: AgentSolutionCardProps) {
  const { agentVersions, setSolutionForVersion } = useAgentBuilderStore();

  // Get the solution for the current version
  const currentVersion = agentVersions.find((v) => v.id === currentVersionId);
  const currentSolution = currentVersion?.solution || null;

  // tRPC mutation for generating solution
  const generateSolutionMutation =
    trpc.agentBuilder.generateSolution.useMutation({
      onSuccess: (data) => {
        if (currentVersionId && data.success && data.solution) {
          setSolutionForVersion(currentVersionId, {
            id: data.solution.id,
            name: data.solution.name,
            description: data.solution.description,
            prompt: data.solution.prompt,
            inputs: data.solution.inputs,
            outputs: data.solution.outputs,
            versionId: currentVersionId,
          });
        }
      },
      onError: (error) => {
        console.error("Failed to generate solution:", error);
      },
    });

  const handleGenerateSolution = () => {
    if (!currentVersionId) return;

    generateSolutionMutation.mutate({
      agentSummary: currentAgent,
      versionId: currentVersionId,
    });
  };

  if (!currentVersionId) {
    return null;
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base">Solution</CardTitle>
            <CardDescription className="text-sm">
              Ready-to-use prompt for this agent
            </CardDescription>
          </div>
          <Badge variant="outline" className="text-xs">
            {currentVersionId}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {!currentSolution ? (
          // Generate Solution Button
          <div className="text-center py-6">
            <div className="space-y-4">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Generate Solution</h4>
                <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                  Create a ready-to-use prompt that implements this agent&apos;s
                  functionality
                </p>
              </div>
              <Button
                onClick={handleGenerateSolution}
                disabled={generateSolutionMutation.isLoading}
                className="gap-2"
              >
                {generateSolutionMutation.isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    Generate Solution
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          // Generated Solution Display
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">{currentSolution.name}</h4>
              <p className="text-sm text-muted-foreground">
                {currentSolution.description}
              </p>
            </div>

            {/* Inputs and Outputs */}
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div className="space-y-1">
                <span className="font-medium text-muted-foreground">
                  Inputs:
                </span>
                <div className="flex flex-wrap gap-1">
                  {currentSolution.inputs.map((input, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {input}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="space-y-1">
                <span className="font-medium text-muted-foreground">
                  Outputs:
                </span>
                <div className="flex flex-wrap gap-1">
                  {currentSolution.outputs.map((output, index) => (
                    <Badge
                      key={index}
                      variant="default"
                      className="text-xs bg-green-100 text-green-800"
                    >
                      {output}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <AIPromptButton
                prompt={currentSolution.prompt}
                className="flex-1"
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
