"use client";
import { useState } from "react";
import { ChevronDown, Database, FileText, BarChart3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { AgentConfiguration as AgentConfigType } from "@/types/agent-builder";

export function AgentConfiguration() {
  const [currentVersion] = useState("v2");

  const mockConfig: AgentConfigType = {
    id: "1",
    name: "AI Agent",
    description: "This is your AI agent configuration",
    currentVersion: "v2",
    versions: [
      { id: "1", version: "v1", createdAt: "2024-01-01" },
      { id: "2", version: "v2", createdAt: "2024-01-02" },
    ],
    inputs: [
      {
        id: "1",
        type: "database_schema",
        label: "database schema",
        icon: "database",
      },
    ],
    outputs: [
      {
        id: "1",
        type: "python_script",
        label: "python script",
        icon: "file-text",
      },
    ],
    process:
      "brainstorm as many charts and figures we can calculate based on the database schema and output a python script that generates these",
  };

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case "database":
        return <Database className="h-4 w-4" />;
      case "file-text":
        return <FileText className="h-4 w-4" />;
      case "bar-chart":
        return <BarChart3 className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              {currentVersion}
              <ChevronDown className="h-3 w-3" />
            </Button>
          </div>
          <span className="text-sm text-muted-foreground">
            {mockConfig.versions.length} versions
          </span>
        </div>

        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-primary rounded-full"></div>
          <span className="font-medium">{mockConfig.name}</span>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          {mockConfig.description}
        </p>
      </div>

      {/* Configuration Sections */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* You provide section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">You provide</h3>
          <div className="space-y-2">
            {mockConfig.inputs.map((input) => (
              <div
                key={input.id}
                className="flex items-center gap-3 p-3 bg-muted rounded-lg"
              >
                {getIcon(input.icon)}
                <Badge variant="secondary" className="text-xs">
                  {input.label}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* I will generate section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">I will generate</h3>
          <div className="space-y-2">
            {mockConfig.outputs.map((output) => (
              <div
                key={output.id}
                className="flex items-center gap-3 p-3 bg-muted rounded-lg"
              >
                {getIcon(output.icon)}
                <Badge variant="success" className="text-xs">
                  {output.label}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* Process section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Process</h3>
          <div className="flex items-start gap-3 p-3 bg-muted rounded-lg">
            {getIcon("bar-chart")}
            <p className="text-sm text-muted-foreground leading-relaxed">
              {mockConfig.process}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
