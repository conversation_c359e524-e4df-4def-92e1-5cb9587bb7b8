"use client";
import { useState, useEffect } from "react";
import {
  ChevronDown,
  Database,
  FileText,
  BarChart3,
  MessageSquare,
  Code,
  Globe,
  Mic,
  Video,
  Image,
  BarChart,
  FileCode,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AgentSummary } from "@/types/agent-builder-response";

interface AgentConfigurationProps {
  agentSummary?: AgentSummary;
}

export function AgentConfiguration({ agentSummary }: AgentConfigurationProps) {
  const [currentVersion, setCurrentVersion] = useState("v1");

  // Update version when agentSummary changes
  useEffect(() => {
    if (agentSummary?.version) {
      setCurrentVersion(agentSummary.version);
    }
  }, [agentSummary]);

  // Use provided agentSummary or fallback to default
  const displayConfig = agentSummary || {
    name: "AI Agent",
    description: "This is your AI agent configuration",
    inputs: [
      {
        id: "1",
        type: "text",
        label: "user input",
        icon: "message-square",
        description: "Text input from the user",
      },
    ],
    outputs: [
      {
        id: "1",
        type: "text",
        label: "response",
        icon: "message-square",
        description: "AI generated response",
      },
    ],
    process: "Process user input and generate a helpful response",
    version: "v1",
  };

  // Enhanced icon mapping function
  const getIcon = (iconName: string) => {
    const iconMap: Record<string, JSX.Element> = {
      // Content types
      "message-square": (
        <MessageSquare className="h-4 w-4 text-muted-foreground" />
      ),
      "file-text": <FileText className="h-4 w-4 text-muted-foreground" />,
      image: <Image className="h-4 w-4 text-muted-foreground" />,
      mic: <Mic className="h-4 w-4 text-muted-foreground" />,
      video: <Video className="h-4 w-4 text-muted-foreground" />,

      // Data types
      database: <Database className="h-4 w-4 text-muted-foreground" />,
      globe: <Globe className="h-4 w-4 text-muted-foreground" />,

      // Code types
      code: <Code className="h-4 w-4 text-muted-foreground" />,
      "file-code": <FileCode className="h-4 w-4 text-muted-foreground" />,

      // Output types
      "bar-chart": <BarChart className="h-4 w-4 text-muted-foreground" />,
      "bar-chart-3": <BarChart3 className="h-4 w-4 text-muted-foreground" />,
    };

    return (
      iconMap[iconName] || (
        <FileText className="h-4 w-4 text-muted-foreground" />
      )
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              {currentVersion}
              <ChevronDown className="h-3 w-3" />
            </Button>
          </div>
          <span className="text-sm text-muted-foreground">1 version</span>
        </div>

        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-primary rounded-full"></div>
          <span className="font-medium">{displayConfig.name}</span>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          {displayConfig.description}
        </p>
      </div>

      {/* Configuration Sections */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* You provide section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">You provide</h3>
          <div className="space-y-2">
            {displayConfig.inputs.map((input) => (
              <div
                key={input.id}
                className="flex items-center gap-3 p-3 bg-muted rounded-lg"
              >
                {getIcon(input.icon)}
                <div className="flex flex-col gap-1">
                  <Badge variant="secondary" className="text-xs w-fit">
                    {input.label}
                  </Badge>
                  {input.description && (
                    <span className="text-xs text-muted-foreground">
                      {input.description}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* I will generate section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">I will generate</h3>
          <div className="space-y-2">
            {displayConfig.outputs.map((output) => (
              <div
                key={output.id}
                className="flex items-center gap-3 p-3 bg-muted rounded-lg"
              >
                {getIcon(output.icon)}
                <div className="flex flex-col gap-1">
                  <Badge
                    variant="default"
                    className="text-xs w-fit bg-green-100 text-green-800 hover:bg-green-200"
                  >
                    {output.label}
                  </Badge>
                  {output.description && (
                    <span className="text-xs text-muted-foreground">
                      {output.description}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Process section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Process</h3>
          <div className="flex items-start gap-3 p-3 bg-muted rounded-lg">
            {getIcon("bar-chart")}
            <p className="text-sm text-muted-foreground leading-relaxed">
              {displayConfig.process}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
