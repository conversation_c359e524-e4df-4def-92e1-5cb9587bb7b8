"use client";
import {
  ChevronDown,
  Database,
  FileText,
  BarChart3,
  MessageSquare,
  Code,
  Globe,
  Mic,
  Video,
  Image,
  BarChart,
  FileCode,
  Sparkles,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { useAgentBuilderStore } from "@/stores/agentBuilderStore";

export function AgentConfiguration() {
  const {
    getCurrentAgent,
    agentVersions,
    currentVersionId,
    setCurrentVersion,
  } = useAgentBuilderStore();
  const currentAgent = getCurrentAgent();

  // If no agent is selected, show empty state
  if (!currentAgent) {
    return (
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                No agent selected
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-muted rounded-full"></div>
            <span className="font-medium text-muted-foreground">
              Create an agent to get started
            </span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Use the chat interface to describe what you want your AI agent to do
          </p>
        </div>

        {/* Empty state content */}
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
              <Sparkles className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">No Agent Yet</h3>
              <p className="text-muted-foreground max-w-sm">
                Start a conversation in the chat to create your first AI agent.
                Describe what you want it to do!
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Enhanced icon mapping function
  const getIcon = (iconName: string) => {
    const iconMap: Record<string, JSX.Element> = {
      // Content types
      "message-square": (
        <MessageSquare className="h-4 w-4 text-muted-foreground" />
      ),
      "file-text": <FileText className="h-4 w-4 text-muted-foreground" />,
      image: <Image className="h-4 w-4 text-muted-foreground" />,
      mic: <Mic className="h-4 w-4 text-muted-foreground" />,
      video: <Video className="h-4 w-4 text-muted-foreground" />,

      // Data types
      database: <Database className="h-4 w-4 text-muted-foreground" />,
      globe: <Globe className="h-4 w-4 text-muted-foreground" />,

      // Code types
      code: <Code className="h-4 w-4 text-muted-foreground" />,
      "file-code": <FileCode className="h-4 w-4 text-muted-foreground" />,

      // Output types
      "bar-chart": <BarChart className="h-4 w-4 text-muted-foreground" />,
      "bar-chart-3": <BarChart3 className="h-4 w-4 text-muted-foreground" />,
    };

    return (
      iconMap[iconName] || (
        <FileText className="h-4 w-4 text-muted-foreground" />
      )
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  {currentVersionId || "v1"}
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                {agentVersions.map((version) => (
                  <DropdownMenuItem
                    key={version.id}
                    onClick={() => setCurrentVersion(version.id)}
                    className={
                      currentVersionId === version.id ? "bg-accent" : ""
                    }
                  >
                    {version.version}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <span className="text-sm text-muted-foreground">
            {agentVersions.length} version
            {agentVersions.length !== 1 ? "s" : ""}
          </span>
        </div>

        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-primary rounded-full"></div>
          <span className="font-medium">{currentAgent.name}</span>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          {currentAgent.description}
        </p>
      </div>

      {/* Configuration Sections */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* You provide section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">You provide</h3>
          <div className="space-y-2">
            {currentAgent.inputs.map((input) => (
              <div
                key={input.id}
                className="flex items-center gap-3 p-3 bg-muted rounded-lg"
              >
                {getIcon(input.icon)}
                <div className="flex flex-col gap-1">
                  <Badge variant="secondary" className="text-xs w-fit">
                    {input.label}
                  </Badge>
                  {input.description && (
                    <span className="text-xs text-muted-foreground">
                      {input.description}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* I will generate section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">I will generate</h3>
          <div className="space-y-2">
            {currentAgent.outputs.map((output) => (
              <div
                key={output.id}
                className="flex items-center gap-3 p-3 bg-muted rounded-lg"
              >
                {getIcon(output.icon)}
                <div className="flex flex-col gap-1">
                  <Badge
                    variant="default"
                    className="text-xs w-fit bg-green-100 text-green-800 hover:bg-green-200"
                  >
                    {output.label}
                  </Badge>
                  {output.description && (
                    <span className="text-xs text-muted-foreground">
                      {output.description}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Process section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Process</h3>
          <div className="flex items-start gap-3 p-3 bg-muted rounded-lg">
            {getIcon("bar-chart")}
            <p className="text-sm text-muted-foreground leading-relaxed">
              {currentAgent.process}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
