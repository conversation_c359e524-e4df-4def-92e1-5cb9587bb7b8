"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Send, User, Bot, RotateCcw, <PERSON>rk<PERSON> } from "lucide-react";
import { useAgentBuilder } from "@/hooks/useAgentBuilder";

import { useAgentBuilderStore } from "@/stores/agentBuilderStore";

export function ChatInterface() {
  const [input, setInput] = useState("");
  const { messages, isLoading, sendMessage, reset } = useAgentBuilder();

  const { setCurrentVersion } = useAgentBuilderStore();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const message = input.trim();
    setInput("");
    await sendMessage(message);

    // Agent update is handled automatically by the store
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  // Helper function to render version badge
  const renderVersionBadge = (messageIndex: number) => {
    // Calculate the version number based on how many agent responses came before this one
    let agentResponseCount = 0;
    for (let i = 0; i <= messageIndex; i++) {
      if (
        messages[i]?.type === "assistant" &&
        messages[i]?.structuredResponse
      ) {
        agentResponseCount++;
      }
    }

    const versionId = `v${agentResponseCount}`;

    return (
      <div className="mt-2">
        <button
          onClick={() => setCurrentVersion(versionId)}
          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary hover:bg-primary/20 transition-colors"
        >
          <Sparkles className="h-3 w-3 mr-1" />
          Agent Created • {versionId}
        </button>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h2 className="text-lg font-semibold">Agent Builder Chat</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={reset}
            disabled={isLoading}
            className="flex items-center gap-1"
          >
            <RotateCcw className="h-3 w-3" />
            Reset
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              <Sparkles className="h-8 w-8 text-primary" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">
                Welcome to Agent Builder
              </h3>
              <p className="text-muted-foreground max-w-md">
                Describe what you want your AI agent to do, and I&apos;ll help
                you create a structured configuration with inputs, outputs, and
                processes.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 justify-center">
              <Badge variant="outline" className="text-xs">
                &quot;Create an agent that analyzes data&quot;
              </Badge>
              <Badge variant="outline" className="text-xs">
                &quot;Build a code generator&quot;
              </Badge>
              <Badge variant="outline" className="text-xs">
                &quot;Make a content summarizer&quot;
              </Badge>
            </div>
          </div>
        )}

        {messages.map((message, index) => {
          const isUser = message.type === "user";
          return (
            <div
              key={message.id}
              className={`flex gap-3 ${isUser ? "justify-end" : "justify-start"}`}
            >
              {!isUser && (
                <div className="flex-shrink-0 w-8 h-8 bg-chart-3 rounded-full flex items-center justify-center">
                  <Bot className="h-4 w-4 text-white" />
                </div>
              )}

              <div className="flex flex-col gap-1 max-w-[80%]">
                <div
                  className={`rounded-lg p-3 ${
                    isUser ? "bg-primary text-primary-foreground" : "bg-muted"
                  }`}
                >
                  {message.content}
                </div>

                {/* Show version badge for assistant messages */}
                {!isUser &&
                  message.structuredResponse &&
                  renderVersionBadge(index)}
              </div>

              {isUser && (
                <div className="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          );
        })}

        {isLoading && (
          <div className="flex gap-3 justify-start">
            <div className="flex-shrink-0 w-8 h-8 bg-chart-3 rounded-full flex items-center justify-center">
              <Bot className="h-4 w-4 text-white" />
            </div>
            <div className="flex flex-col gap-1">
              <div className="rounded-lg p-3 bg-muted">
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Building your agent...
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder="Describe what you want your agent to do..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={isLoading || !input.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
}
