"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Send, User, Bot, Undo2, RotateCcw, <PERSON>rkles } from "lucide-react";
import { useAgentBuilder } from "@/hooks/useAgentBuilder";
import {
  AgentBuilderResponse,
  AgentUpdateResponse,
} from "@/types/agent-builder-response";

interface ChatInterfaceProps {
  onAgentUpdate?: (agentSummary: any) => void;
}

export function ChatInterface({ onAgentUpdate }: ChatInterfaceProps) {
  const [input, setInput] = useState("");
  const {
    messages,
    isLoading,
    sendMessage,
    resetAgent,
    undoLastChange,
    canUndo,
    currentAgentSummary,
  } = useAgentBuilder();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const message = input.trim();
    setInput("");
    await sendMessage(message);

    // Notify parent component of agent update
    if (onAgentUpdate) {
      onAgentUpdate(currentAgentSummary);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  // Helper function to render structured response info
  const renderStructuredResponseInfo = (
    structuredResponse: AgentBuilderResponse | AgentUpdateResponse
  ) => {
    const isUpdate = "changes" in structuredResponse;

    return (
      <div className="mt-2 space-y-1">
        <div className="flex items-center gap-2">
          <Sparkles className="h-3 w-3 text-primary" />
          <span className="text-xs text-primary font-medium">
            {isUpdate ? "Agent Updated" : "Agent Created"} •{" "}
            {currentAgentSummary.version}
          </span>
        </div>

        {isUpdate &&
          structuredResponse.changes &&
          structuredResponse.changes.length > 0 && (
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">Changes:</span>{" "}
              {structuredResponse.changes
                .map((change) => change.field)
                .join(", ")}
            </div>
          )}

        {structuredResponse.suggestions &&
          structuredResponse.suggestions.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-1">
              {structuredResponse.suggestions
                .slice(0, 2)
                .map((suggestion, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {suggestion}
                  </Badge>
                ))}
            </div>
          )}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h2 className="text-lg font-semibold">Agent Builder Chat</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={undoLastChange}
            disabled={!canUndo || isLoading}
            className="flex items-center gap-1"
          >
            <Undo2 className="h-3 w-3" />
            Undo
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetAgent}
            disabled={isLoading}
            className="flex items-center gap-1"
          >
            <RotateCcw className="h-3 w-3" />
            Reset
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              <Sparkles className="h-8 w-8 text-primary" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">
                Welcome to Agent Builder
              </h3>
              <p className="text-muted-foreground max-w-md">
                Describe what you want your AI agent to do, and I'll help you
                create a structured configuration with inputs, outputs, and
                processes.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 justify-center">
              <Badge variant="outline" className="text-xs">
                "Create an agent that analyzes data"
              </Badge>
              <Badge variant="outline" className="text-xs">
                "Build a code generator"
              </Badge>
              <Badge variant="outline" className="text-xs">
                "Make a content summarizer"
              </Badge>
            </div>
          </div>
        )}

        {messages.map((message) => {
          const isUser = message.type === "user";
          return (
            <div
              key={message.id}
              className={`flex gap-3 ${isUser ? "justify-end" : "justify-start"}`}
            >
              {!isUser && (
                <div className="flex-shrink-0 w-8 h-8 bg-chart-3 rounded-full flex items-center justify-center">
                  <Bot className="h-4 w-4 text-white" />
                </div>
              )}

              <div className="flex flex-col gap-1 max-w-[80%]">
                <div
                  className={`rounded-lg p-3 ${
                    isUser ? "bg-primary text-primary-foreground" : "bg-muted"
                  }`}
                >
                  {message.content}
                </div>

                {/* Show structured response info for assistant messages */}
                {!isUser &&
                  message.structuredResponse &&
                  renderStructuredResponseInfo(message.structuredResponse)}
              </div>

              {isUser && (
                <div className="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          );
        })}

        {isLoading && (
          <div className="flex gap-3 justify-start">
            <div className="flex-shrink-0 w-8 h-8 bg-chart-3 rounded-full flex items-center justify-center">
              <Bot className="h-4 w-4 text-white" />
            </div>
            <div className="flex flex-col gap-1">
              <div className="rounded-lg p-3 bg-muted">
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Building your agent...
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder="Describe what you want your agent to do..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={isLoading || !input.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
}
