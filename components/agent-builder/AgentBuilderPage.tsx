"use client";
import { useEffect, useState } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { ChatInterface } from "./ChatInterface";
import { AgentConfiguration } from "./AgentConfiguration";

const PANEL_STORAGE_KEY = "agent-builder-panel-sizes";
const DEFAULT_CHAT_SIZE = 60;
const DEFAULT_CONFIG_SIZE = 40;

export function AgentBuilderPage() {
  const [isClient, setIsClient] = useState(false);
  const [panelSizes, setPanelSizes] = useState({
    chat: DEFAULT_CHAT_SIZE,
    config: DEFAULT_CONFIG_SIZE,
  });

  // Ensure we're on the client side before accessing localStorage
  useEffect(() => {
    setIsClient(true);

    // Load saved panel sizes from localStorage
    const savedSizes = localStorage.getItem(PANEL_STORAGE_KEY);
    if (savedSizes) {
      try {
        const parsed = JSON.parse(savedSizes);
        setPanelSizes(parsed);
      } catch (error) {
        console.warn("Failed to parse saved panel sizes:", error);
      }
    }
  }, []);

  // Save panel sizes to localStorage when they change
  const handlePanelResize = (sizes: number[]) => {
    const newSizes = {
      chat: sizes[0] || DEFAULT_CHAT_SIZE,
      config: sizes[1] || DEFAULT_CONFIG_SIZE,
    };

    setPanelSizes(newSizes);

    if (isClient) {
      localStorage.setItem(PANEL_STORAGE_KEY, JSON.stringify(newSizes));
    }
  };

  // Don't render panels until we're on the client to avoid hydration mismatch
  if (!isClient) {
    return (
      <div className="h-screen bg-background flex">
        <div className="flex-1 border-r border-border">
          <ChatInterface />
        </div>
        <div className="w-96">
          <AgentConfiguration />
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-background">
      <PanelGroup direction="horizontal" onLayout={handlePanelResize}>
        {/* Left Panel - Chat Interface */}
        <Panel defaultSize={panelSizes.chat} minSize={30}>
          <div className="h-full border-r border-border">
            <ChatInterface />
          </div>
        </Panel>

        {/* Resize Handle */}
        <PanelResizeHandle className="w-2 bg-border hover:bg-accent transition-colors duration-200 cursor-col-resize" />

        {/* Right Panel - AI Agent Configuration */}
        <Panel defaultSize={panelSizes.config} minSize={25}>
          <div className="h-full">
            <AgentConfiguration />
          </div>
        </Panel>
      </PanelGroup>
    </div>
  );
}
