"use client";
import { ChatInterface } from "./ChatInterface";
import { AgentConfiguration } from "./AgentConfiguration";

export function AgentBuilderPage() {
  return (
    <div className="flex h-screen bg-background">
      {/* Left Panel - Chat Interface */}
      <div className="flex-1 border-r border-border">
        <ChatInterface />
      </div>

      {/* Right Panel - AI Agent Configuration */}
      <div className="w-96 border-l border-border">
        <AgentConfiguration />
      </div>
    </div>
  );
}
