"use client";
import { Panel, PanelGroup, PanelResizeH<PERSON>le } from "react-resizable-panels";
import { ChatInterface } from "./ChatInterface";
import { AgentConfiguration } from "./AgentConfiguration";

export function AgentBuilderPage() {
  return (
    <div className="h-screen bg-background">
      <PanelGroup direction="horizontal">
        {/* Left Panel - Chat Interface */}
        <Panel defaultSize={60} minSize={30}>
          <div className="h-full border-r border-border">
            <ChatInterface />
          </div>
        </Panel>

        {/* Resize Handle */}
        <PanelResizeHandle className="w-2 bg-border hover:bg-accent transition-colors duration-200 cursor-col-resize" />

        {/* Right Panel - AI Agent Configuration */}
        <Panel defaultSize={40} minSize={25}>
          <div className="h-full">
            <AgentConfiguration />
          </div>
        </Panel>
      </PanelGroup>
    </div>
  );
}
