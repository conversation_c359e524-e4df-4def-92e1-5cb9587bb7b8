"use client";
import { useState, useCallback } from "react";
import { ChatInterface } from "./ChatInterface";
import { AgentConfiguration } from "./AgentConfiguration";
import {
  AgentSummary,
  defaultAgentSummary,
} from "@/types/agent-builder-response";

export function AgentBuilderPage() {
  const [currentAgentSummary, setCurrentAgentSummary] =
    useState<AgentSummary>(defaultAgentSummary);

  const handleAgentUpdate = useCallback((agentSummary: AgentSummary) => {
    setCurrentAgentSummary(agentSummary);
  }, []);

  return (
    <div className="flex h-screen bg-background">
      {/* Left Panel - Chat Interface */}
      <div className="flex-1 border-r border-border">
        <ChatInterface onAgentUpdate={handleAgentUpdate} />
      </div>

      {/* Right Panel - AI Agent Configuration */}
      <div className="w-96 border-l border-border">
        <AgentConfiguration agentSummary={currentAgentSummary} />
      </div>
    </div>
  );
}
