import { create } from 'zustand';
import { AgentSummary, AgentBuilderResponse } from '@/types/agent-builder-response';
import { ChatMessage } from '@/types/agent-builder';

interface AgentVersion {
  id: string;
  version: string;
  agentSummary: AgentSummary;
  createdAt: string;
}

interface AgentBuilderState {
  // Agent versions
  agentVersions: AgentVersion[];
  currentVersionId: string | null;
  
  // Chat state
  messages: ChatMessage[];
  isLoading: boolean;
  
  // Actions
  addAgentVersion: (response: AgentBuilderResponse) => void;
  setCurrentVersion: (versionId: string) => void;
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  setLoading: (loading: boolean) => void;
  reset: () => void;
  
  // Getters
  getCurrentAgent: () => AgentSummary | null;
  getVersionByNumber: (versionNumber: number) => AgentVersion | null;
}

let messageIdCounter = 0;

export const useAgentBuilderStore = create<AgentBuilderState>((set, get) => ({
  // Initial state
  agentVersions: [],
  currentVersionId: null,
  messages: [],
  isLoading: false,

  // Actions
  addAgentVersion: (response: AgentBuilderResponse) => {
    const state = get();
    const versionNumber = state.agentVersions.length + 1;
    const newVersion: AgentVersion = {
      id: `v${versionNumber}`,
      version: `v${versionNumber}`,
      agentSummary: response.agentSummary,
      createdAt: new Date().toISOString(),
    };

    set({
      agentVersions: [...state.agentVersions, newVersion],
      currentVersionId: newVersion.id,
    });
  },

  setCurrentVersion: (versionId: string) => {
    set({ currentVersionId: versionId });
  },

  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    messageIdCounter += 1;
    const newMessage: ChatMessage = {
      ...message,
      id: `msg-${messageIdCounter}-${Date.now()}`,
      timestamp: new Date().toISOString(),
    };

    set((state) => ({
      messages: [...state.messages, newMessage],
    }));
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  reset: () => {
    set({
      agentVersions: [],
      currentVersionId: null,
      messages: [],
      isLoading: false,
    });
    messageIdCounter = 0;
  },

  // Getters
  getCurrentAgent: () => {
    const state = get();
    if (!state.currentVersionId) return null;
    
    const currentVersion = state.agentVersions.find(
      (v) => v.id === state.currentVersionId
    );
    return currentVersion?.agentSummary || null;
  },

  getVersionByNumber: (versionNumber: number) => {
    const state = get();
    return state.agentVersions.find((v) => v.version === `v${versionNumber}`) || null;
  },
}));
